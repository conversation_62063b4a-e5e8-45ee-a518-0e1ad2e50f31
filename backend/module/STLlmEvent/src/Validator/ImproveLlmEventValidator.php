<?php

namespace STLlmEvent\Validator;

use STLib\Validator\Check\AiInput;
use STLib\Validator\Validator;

class ImproveLlmEventValidator extends Validator
{
    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();
        $textProperties = [
            'name',
            'description',
        ];

        foreach ($textProperties as $propertyName) {
            if (isset($input[$propertyName])) {
                $textContentValidator = new AiInput();

                if (!$textContentValidator->isValid($input[$propertyName])) {
                    $errors = $textContentValidator->getMessages();

                    foreach ($errors as $error) {
                        $this->addError($propertyName, $error);
                    }
                }
            }
        }
    }
}
