<?php

namespace STLlmEvent\Validator;

use STLib\Validator\Check\AiInput;
use STLib\Validator\Validator;

class ImproveLlmEventValidator extends Validator
{
    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();

        // Only validate description with AiInput, not name
        if (isset($input['description'])) {
            $textContentValidator = new AiInput();

            if (!$textContentValidator->isValid($input['description'])) {
                $errors = $textContentValidator->getMessages();

                foreach ($errors as $error) {
                    $this->addError('description', $error);
                }
            }
        }
    }
}
