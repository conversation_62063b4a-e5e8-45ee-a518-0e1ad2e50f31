<?php

declare(strict_types=1);

namespace Console\Command\Call\ClientSummary;

use Console\Command\BaseCommand;
use STCall\Service\CallService;
use STCall\Service\ClientSummaryService;
use STCompany\Service\CompanyService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'call:client-summary:update',
    description: 'Run client summary update',
)]
final class ClientSummaryUpdateCommand extends BaseCommand
{
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        /** @var CompanyService $companyService */
        $companyService = $this->getServiceManager()->get(CompanyService::class);
        /** @var ClientSummaryService $clientSummaryService */
        $clientSummaryService = $this->getServiceManager()->get(ClientSummaryService::class);

        foreach ($companyService->getCompanyIdsWithClientSummaryEnabled() as $companyId) {
            $clientIds = $companyService->getCompanyClientsIds($companyId);

            foreach ($clientIds as $clientId) {
                $clientSummaryService->publishClientSummaryUpdate($companyId, $clientId);
            }
        }

        return self::FAILURE;
    }
}
