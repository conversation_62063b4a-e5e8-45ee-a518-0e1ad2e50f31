<?php

declare(strict_types=1);

namespace STCall\Daemon;

use Carbon\Carbon;
use STAlgo\Service\AiSolutionsCommutatorService;
use STApi\Entity\Exception\ThirdPartyApiException;
use ST<PERSON>all\Entity\Call;
use STCall\Entity\CallCollection;
use STCall\Service\CallService;
use STCall\Service\ClientSummaryService;
use STCall\Service\Interfaces\CompanySelectorInterface;
use STCompany\Entity\Company;
use STRabbit\Entity\AbstractDaemon;
use STRoboTruck\Service\DataCollection\DataCollector;

class ClientSummaryDaemon extends AbstractDaemon
{
    public const string QUEUE = 'client-summary';
    public const string QUEUE_ERROR = 'client-summary-error';

    public function __construct(
        private readonly CallService $callService,
        private readonly CompanySelectorInterface $companySelector,
        private readonly ClientSummaryService $clientSummaryService,
        private readonly AiSolutionsCommutatorService $aiSolutionsCommutator,
        private readonly DataCollector $dataCollector
    ) {
    }

    public function handle(string $message): void
    {
        $data = json_decode($message, true);

        if (!isset($data['company_id']) || !isset($data['client_id'])) {
            $this->dataCollector->collect(
                DataCollector::EVENT_CALL_CLIENT_SUMMARY_MESSAGE_FAIL,
                'Failed to get required data from message',
                ['message' => $message]
            );
        }

        $company = $this->companySelector->getCompany((int) $data['company_id']);
        $clientId = (string) $data['client_id'];

        $lastSummary = $this->clientSummaryService->getLastSummary(
            $company->getId(),
            $clientId,
        );

        $startDate = $lastSummary?->getLastCallCreated();
        $calls = $this->callService->getClientCallsByDateRange(
            $company,
            $clientId,
            startDate: $startDate,
            limit: 1,
        );

        if ($calls->isEmpty()) {
            return;
        }

        $this->generateSummaryFromCalls(
            $company,
            $clientId,
            $calls->first()->getTime(),
        );
    }

    private function generateSummaryFromCalls(
        Company $company,
        string $clientId,
        Carbon $firstCallTime,
    ): void {
        // Get client summary with last_call_time less than call time of the earliest call
        $previousSummary = $this->clientSummaryService->getLastSummary(
            $company->getId(),
            $clientId,
            $firstCallTime
        );

        $calls = $this->callService->getClientCallsByDateRange(
            $company,
            $clientId,
            dateColumn: 'call_time',
            startDate: $previousSummary?->getLastCallTime()
        );

        foreach ($calls->chunk(10) as $callsChunk) {
            $callsChunk = new CallCollection($callsChunk);
            $lastCallTime = $callsChunk->last()->getTime();

            try {
                $summaryDTO = $this->aiSolutionsCommutator->getClientSummary(
                    $company->getId(),
                    $callsChunk,
                    $previousSummary
                );
            } catch (ThirdPartyApiException $e) {
                throw $e;
            } catch (\Throwable $e) {
                throw new ThirdPartyApiException('Failed to generate client summary: ' . $e->getMessage());
            }

            $callsChunk->usort(function ($a, $b) {
                return $a->getCreated()->lt($b->getCreated()) ? -1 : 1;
            });

            $previousSummary = $this->clientSummaryService->createClientSummaryFromDTO(
                $summaryDTO,
                $company->getId(),
                $clientId,
                $lastCallTime,
                $callsChunk->last()->getCreated()
            );
        }
    }
}
