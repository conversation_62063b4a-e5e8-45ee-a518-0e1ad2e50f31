<?php

declare(strict_types=1);

namespace STCall\Entity;

use Carbon\Carbon;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

/**
 *
 * @todo Create BaseCall. Call and ChatCall should be extended from BaseCall
 */
class Call
{
    use BaseHydratorTrait;

    public const string CALL_TYPE = 'call';
    public const string CALL_SPEAKER_ROLE_UNCLEAR = 'unclear';
    public const string CALL_SPEAKER_ROLE_AGENT = 'agent';
    public const string CALL_SPEAKER_ROLE_CLIENT = 'client';

    /**
     *
     * @var string
     */
    protected string $id;

    /**
     *
     * @var string|null
     */
    protected ?string $originalFileName;

    /**
     *
     * @var int
     */
    protected int $companyId;

    /**
     *
     * @var Carbon
     */
    protected Carbon $time;

    /**
     *
     * @var string|null
     */
    protected ?string $callStatus = 'answered';

    /**
     *
     * @var string|null
     */
    protected ?string $language = null;

    /**
     *
     * @var int
     */
    protected ?int $duration;

    /**
     *
     * @var int|null
     */
    protected ?int $paragraphsCount = 0;

    /**
     *
     * @var string
     */
    protected string $origin = 'api';

    /**
     *
     * @var int
     */
    protected int $agentId;

    /**
     *
     * @var string|null
     */
    protected ?string $agentName = null;

    /**
     *
     * @var string
     */
    protected string $clientId;

    /**
     *
     * @todo Delete this property from call object
     *
     * @var string
     */
    protected string $clientName;

    /**
     *
     * @todo Delete this property from call object
     *
     * @var string|null
     */
    protected ?string $clientStatus = null;

    /**
     *
     * @todo Delete this property from call object
     *
     * @var float|null
     */
    protected ?float $clientValue = null;

    /**
     *
     * @todo Delete this property from call object
     *
     * @var string|null
     */
    protected ?string $clientSource = null;

    /**
     *
     * @todo Delete this property from call object
     *
     * @var Carbon
     */
    protected ?Carbon $clientConvertedDate = null;

    /**
     *
     * @todo Delete this property from call object
     *
     * @var Carbon
     */
    protected ?Carbon $clientLastTransactionDate = null;

    /**
     *
     * @var string|null
     */
    protected ?string $s3FilePath = null;

    /**
     *
     * @var string|null
     */
    protected ?string $fileHash = null;

    /**
     *
     * @var int|null
     */
    protected ?int $uploadedUserId = null;

    /**
     *
     * @var string|null
     */
    protected ?string $uploadedUserName = null;

    /**
     *
     * @var Carbon
     */
    protected ?Carbon $uploadedTime = null;

    /**
     *
     * @var Carbon
     */
    protected ?Carbon $transcribedAt = null;

    /**
     * @var Carbon|null
     */
    protected ?Carbon $analyzedAt = null;

    /**
     *
     * @var string|null
     */
    protected ?string $transcribingDriver = null;

    /**
     *
     * @var string|null
     */
    protected ?string $translationDriver = null;

    /**
     *
     * @var bool
     */
    protected bool $isTranscribed = false;

    /**
     *
     * @var bool
     */
    protected bool $isTranslated = false;

    /**
     *
     * @var bool
     */
    protected bool $isSpeakersRolesDetected = false;

    /**
     *
     * @var bool
     */
    protected bool $isChecklistCompleted = false;

    /**
     * @var bool
     */
    protected bool $isSummarizationCompleted = false;

    /**
     * @var bool
     */
    protected bool $isLlmEventsDetected = false;

    /**
     *
     * @var bool
     */
    protected bool $isAnalyzed = false;

    /**
     *
     * @var bool
     */
    protected bool $isDeleted = false;

    /**
     *
     * @var bool
     */
    protected bool $isRunManually = false;

    /**
     *
     * @var bool
     */
    protected bool $isSentToTranscribing = false;

    /**
     *
     * @var array
     */
    protected array $speakers = [];

    /**
     *
     * @var Carbon
     */
    protected ?Carbon $created = null;
    protected ?Carbon $createdAt = null;

    /**
     *
     * @var ParagraphCollection|null
     */
    protected ?ParagraphCollection $paragraphs = null;

    /**
     *
     * @var CommentCollection|null
     */
    protected ?CommentCollection $comments = null;

    /**
     *
     * @var Paragraph|null
     */
    protected ?Paragraph $paragraph = null;

    /**
     *
     * @var CallFragment\CallFragmentCollection|null
     */
    protected ?CallFragment\CallFragmentCollection $fragments = null;

    /**
     *
     * @var EventHappeningCollection|null
     */
    protected ?EventHappeningCollection $reviewedEventHappenings = null;

    /**
     *
     * @var CalculatedEvent\CalculatedEventsCollection|null
     */
    protected ?CalculatedEvent\CalculatedEventsCollection $calculatedEvents = null;

    /**
     *
     * @var array
     */
    protected array $reviewers = [];

    /**
     *
     * @var bool
     */
    protected bool $isReviewed = false;

    /**
     *
     * @var int|null
     */
    protected ?int $score = null;

    /**
     *
     * @var int|null
     */
    protected ?int $maxScore = null;

    /**
     *
     * @var int|null
     */
    protected ?int $minScore = null;

    /**
     *
     * @var int
     */
    protected int $baseScore = 0;

    /**
     *
     * @var float|null
     */
    protected ?float $riskRank = null;

    /**
     *
     * @var Carbon|null
     */
    protected ?Carbon $reviewedTime = null;

    /**
     *
     * @var int|null
     */
    protected ?int $reviewerUserId = null;

    /**
     *
     * Class constructor
     */
    public function __construct()
    {
        $this->paragraphs = new ParagraphCollection();
        $this->comments = new CommentCollection();
        $this->paragraph = new Paragraph();
    }

    /**
     *
     * @return string
     */
    public function getId(): string
    {
        return $this->id;
    }

    /**
     *
     * @return string|null
     */
    public function getOriginalFileName(): ?string
    {
        return $this->originalFileName;
    }

    /**
     *
     * @return int
     */
    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    /**
     *
     * @return Carbon
     */
    public function getTime(): Carbon
    {
        return $this->time;
    }

    /**
     *
     * @return string|null
     */
    public function getCallStatus(): ?string
    {
        return $this->callStatus;
    }

    /**
     *
     * @return string|null
     */
    public function getLanguage(): ?string
    {
        return $this->language;
    }

    /**
     *
     * @return int|null
     */
    public function getDuration(): ?int
    {
        return $this->duration;
    }

    /**
     *
     * @return int|null
     */
    public function getParagraphsCount(): ?int
    {
        return $this->paragraphsCount;
    }

    /**
     *
     * @return string
     */
    public function getOrigin(): string
    {
        return $this->origin;
    }

    /**
     *
     * @return int
     */
    public function getAgentId(): int
    {
        return $this->agentId;
    }

    /**
     *
     * @return string|null
     */
    public function getAgentName(): ?string
    {
        return $this->agentName;
    }

    /**
     *
     * @return string
     */
    public function getClientId(): string
    {
        return $this->clientId;
    }

    /**
     *
     * @return string
     */
    public function getClientName(): string
    {
        return $this->clientName;
    }

    /**
     *
     * @return string|null
     */
    public function getClientStatus(): ?string
    {
        return $this->clientStatus;
    }

    /**
     *
     * @return float|null
     */
    public function getClientValue(): ?float
    {
        return $this->clientValue;
    }

    /**
     *
     * @return string|null
     */
    public function getClientSource(): ?string
    {
        return $this->clientSource;
    }

    /**
     *
     * @return Carbon|null
     */
    public function getClientConvertedDate(): ?Carbon
    {
        return $this->clientConvertedDate;
    }

    /**
     *
     * @return Carbon|null
     */
    public function getClientLastTransactionDate(): ?Carbon
    {
        return $this->clientLastTransactionDate;
    }

    /**
     *
     * @return string|null
     */
    public function getS3FilePath(): ?string
    {
        return $this->s3FilePath;
    }

    /**
     *
     * @return string|null
     */
    public function getFileHash(): ?string
    {
        return $this->fileHash;
    }

    /**
     *
     * @return int|null
     */
    public function getUploadedUserId(): ?int
    {
        return $this->uploadedUserId;
    }

    /**
     *
     * @return string|null
     */
    public function getUploadedUserName(): ?string
    {
        return $this->uploadedUserName;
    }

    /**
     *
     * @return Carbon|null
     */
    public function getUploadedTime(): ?Carbon
    {
        return $this->uploadedTime;
    }

    /**
     * @return Carbon|null
     */
    public function getTranscribedAt(): ?Carbon
    {
        return $this->transcribedAt;
    }

    /**
     * @param Carbon|string|null $transcribedAt
     * @return $this
     */
    public function setTranscribedAt(Carbon|string|null $transcribedAt): Call
    {
        $this->transcribedAt = is_string($transcribedAt) ? Carbon::parse($transcribedAt) : $transcribedAt;
        return $this;
    }

    /**
     * @return Carbon|null
     */
    public function getAnalyzedAt(): ?Carbon
    {
        return $this->analyzedAt;
    }

    /**
     * @param Carbon|string|null $analyzedAt
     * @return $this
     */
    public function setAnalyzedAt(Carbon|string|null $analyzedAt): Call
    {
        $this->analyzedAt = is_string($analyzedAt) ? Carbon::parse($analyzedAt) : $analyzedAt;
        return $this;
    }

    /**
     *
     * @return string|null
     */
    public function getTranscribingDriver(): ?string
    {
        return $this->transcribingDriver;
    }

    /**
     *
     * @return string|null
     */
    public function getTranslationDriver(): ?string
    {
        return $this->translationDriver;
    }

    /**
     *
     * @return bool
     */
    public function getIsTranscribed(): bool
    {
        return $this->isTranscribed;
    }

    /**
     *
     * @return bool
     */
    public function getIsTranslated(): bool
    {
        return $this->isTranslated;
    }

    /**
     *
     * @return bool
     */
    public function getIsSpeakersRolesDetected(): bool
    {
        return $this->isSpeakersRolesDetected;
    }

    /**
     *
     * @return bool
     */
    public function getIsChecklistCompleted(): bool
    {
        return $this->isChecklistCompleted;
    }

    public function getIsSummarizationCompleted(): bool
    {
        return $this->isSummarizationCompleted;
    }

    /**
     * @return bool
     */
    public function getIsLlmEventsDetected(): bool
    {
        return $this->isLlmEventsDetected;
    }

    /**
     * @return bool
     */
    public function getIsAnalyzed(): bool
    {
        return $this->isAnalyzed;
    }

    /**
     *
     * @return bool
     */
    public function getIsDeleted(): bool
    {
        return $this->isDeleted;
    }

    /**
     *
     * @return bool
     */
    public function getIsRunManually(): bool
    {
        return $this->isRunManually;
    }

    /**
     *
     * @return Carbon
     */
    public function getCreated(): Carbon
    {
        if (is_null($this->created)) {
            $this->created = Carbon::now();
        }
        return $this->created;
    }

    /**
     *
     * @return string
     */
    public function getCallType(): string
    {
        return static::CALL_TYPE;
    }

    /**
     *
     * @return ParagraphCollection|null
     */
    public function getParagraphs(): ?ParagraphCollection
    {
        return $this->paragraphs;
    }

    /**
     *
     * @return CommentCollection|null
     */
    public function getComments(): ?CommentCollection
    {
        return $this->comments;
    }

    /**
     *
     * @return CallFragment\CallFragmentCollection|null
     */
    public function getFragments(): ?CallFragment\CallFragmentCollection
    {
        return $this->fragments;
    }

    /**
     *
     * @return array
     */
    public function getReviewers(): array
    {
        return $this->reviewers;
    }

    /**
     * @param string $id
     * @return $this
     */
    public function setId(string $id): Call
    {
        $this->id = $id;
        return $this;
    }

    /**
     *
     * @param string|null $originalFileName
     * @return Call
     */
    public function setOriginalFileName(?string $originalFileName): Call
    {
        $this->originalFileName = $originalFileName;
        return $this;
    }

    /**
     *
     * @param int $companyId
     * @return Call
     */
    public function setCompanyId(int $companyId): Call
    {
        $this->companyId = $companyId;
        return $this;
    }

    /**
     *
     * @param Carbon $time
     * @return Call
     */
    public function setTime(string|Carbon $time): Call
    {
        $this->time = is_string($time) ? Carbon::parse($time) : $time;
        return $this;
    }

    /**
     *
     * @param string|null $status
     * @return Call
     */
    public function setCallStatus(?string $status): Call
    {
        $this->callStatus = $status;
        return $this;
    }

    /**
     *
     * @param string|null $language
     * @return Call
     */
    public function setLanguage(?string $language): Call
    {
        $this->language = $language;
        return $this;
    }

    /**
     *
     * @param int|null $duration
     * @return Call
     */
    public function setDuration(?int $duration): Call
    {
        $this->duration = $duration;
        return $this;
    }

    /**
     *
     * @param int|null $paragraphsCount
     * @return Call
     */
    public function setParagraphsCount(?int $paragraphsCount): Call
    {
        $this->paragraphsCount = $paragraphsCount;
        return $this;
    }

    /**
     *
     * @param string $origin
     * @return Call
     */
    public function setOrigin(string $origin): Call
    {
        $this->origin = $origin;
        return $this;
    }

    /**
     *
     * @param int $agentId
     * @return Call
     */
    public function setAgentId(int $agentId): Call
    {
        $this->agentId = $agentId;
        return $this;
    }

    /**
     *
     * @param string|null $agentName
     * @return Call
     */
    public function setAgentName(?string $agentName): Call
    {
        $this->agentName = $agentName;
        return $this;
    }

    /**
     *
     * @param string $clientId
     * @return Call
     */
    public function setClientId(string $clientId): Call
    {
        $this->clientId = $clientId;
        return $this;
    }

    /**
     *
     * @param string $clientName
     * @return Call
     */
    public function setClientName(string $clientName): Call
    {
        $this->clientName = $clientName;
        return $this;
    }

    /**
     *
     * @param string|null $clientStatus
     * @return Call
     */
    public function setClientStatus(?string $clientStatus): Call
    {
        $this->clientStatus = $clientStatus;
        return $this;
    }

    /**
     *
     * @param float|null $clientValue
     * @return Call
     */
    public function setClientValue(?float $clientValue): Call
    {
        $this->clientValue = $clientValue;
        return $this;
    }

    /**
     *
     * @param string|null $clientSource
     * @return Call
     */
    public function setClientSource(?string $clientSource): Call
    {
        $this->clientSource = $clientSource;
        return $this;
    }

    /**
     *
     * @param null|string|Carbon $clientConvertedDate
     * @return Call
     */
    public function setClientConvertedDate(null|string|Carbon $clientConvertedDate): Call
    {
        $this->clientConvertedDate = is_string($clientConvertedDate) ? Carbon::parse($clientConvertedDate) : $clientConvertedDate;
        return $this;
    }

    /**
     *
     * @param null|string|Carbon $clientLastTransactionDate
     * @return Call
     */
    public function setClientLastTransactionDate(null|string|Carbon $clientLastTransactionDate): Call
    {
        $this->clientLastTransactionDate = is_string($clientLastTransactionDate) ? Carbon::parse($clientLastTransactionDate) : $clientLastTransactionDate;
        ;
        return $this;
    }

    /**
     *
     * @param string|null $s3FilePath
     * @return Call
     */
    public function setS3FilePath(?string $s3FilePath): Call
    {
        $this->s3FilePath = $s3FilePath;
        return $this;
    }

    /**
     *
     * @param string|null $fileHash
     * @return Call
     */
    public function setFileHash(?string $fileHash): Call
    {
        $this->fileHash = $fileHash;
        return $this;
    }

    /**
     *
     * @param int|null $uploadedUserId
     */
    public function setUploadedUserId(?int $uploadedUserId): Call
    {
        $this->uploadedUserId = $uploadedUserId;
        return $this;
    }

    /**
     *
     * @param string|null $uploadedUserName
     */
    public function setUploadedUserName(?string $uploadedUserName): Call
    {
        $this->uploadedUserName = $uploadedUserName;
        return $this;
    }

    /**
     *
     * @param string|Carbon $uploadedTime
     * @return Call
     */
    public function setUploadedTime(string|Carbon $uploadedTime): Call
    {
        $this->uploadedTime = is_string($uploadedTime) ? Carbon::parse($uploadedTime) : $uploadedTime;
        return $this;
    }

    /**
     *
     * @param string|null $transcribingDriver
     * @return Call
     */
    public function setTranscribingDriver(?string $transcribingDriver): Call
    {
        $this->transcribingDriver = $transcribingDriver;
        return $this;
    }

    /**
     *
     * @param string|null $translationDriver
     * @return Call
     */
    public function setTranslationDriver(?string $translationDriver): Call
    {
        $this->translationDriver = $translationDriver;
        return $this;
    }

    /**
     *
     * @param bool $isTranscribed
     * @return Call
     */
    public function setIsTranscribed(bool $isTranscribed): Call
    {
        $this->isTranscribed = $isTranscribed;
        return $this;
    }

    /**
     *
     * @param bool $isTranslated
     * @return Call
     */
    public function setIsTranslated(bool $isTranslated): Call
    {
        $this->isTranslated = $isTranslated;
        return $this;
    }

    /**
     * @param bool $isLlmEventsDetected
     * @return $this
     */
    public function setIsLlmEventsDetected(bool $isLlmEventsDetected): Call
    {
        $this->isLlmEventsDetected = $isLlmEventsDetected;

        return $this;
    }

    /**
     *
     * @param bool $isAnalyzed
     * @return Call
     */
    public function setIsAnalyzed(bool $isAnalyzed): Call
    {
        $this->isAnalyzed = $isAnalyzed;
        return $this;
    }

    /**
     *
     * @param bool $isDeleted
     * @return Call
     */
    public function setIsDeleted(bool $isDeleted): Call
    {
        $this->isDeleted = $isDeleted;
        return $this;
    }

    /**
     *
     * @param bool $isRunManually
     * @return Call
     */
    public function setIsRunManually(bool $isRunManually): Call
    {
        $this->isRunManually = $isRunManually;
        return $this;
    }

    /**
     *
     * @param Carbon $created
     * @return Call
     */
    public function setCreated(string|Carbon $created): Call
    {
        $this->created = is_string($created) ? Carbon::parse($created) : $created;
        return $this;
    }

    /**
     *
     * @param ParagraphCollection|null $paragraphs
     * @return Call
     */
    public function setParagraphs(?ParagraphCollection $paragraphs): Call
    {
        $this->paragraphs = $paragraphs;
        return $this;
    }

    /**
     *
     * @param CommentCollection|null $comments
     * @return Call
     */
    public function setComments(?CommentCollection $comments): Call
    {
        $this->comments = $comments;
        return $this;
    }

    /**
     *
     * @param CallFragment\CallFragmentCollection $fragments
     * @return Call
     */
    public function setFragments(CallFragment\CallFragmentCollection $fragments): Call
    {
        $this->fragments = $fragments;
        return $this;
    }

    /**
     *
     * @param array $reviewers
     * @return Call
     */
    public function setReviewers(array $reviewers): Call
    {
        $this->reviewers = $reviewers;
        return $this;
    }

    /**
     *
     * @param bool $isReviewed
     * @return Call
     */
    public function setIsReviewed(bool $isReviewed): Call
    {
        $this->isReviewed = $isReviewed;
        return $this;
    }

    /**
     *
     * @param int|null $maxScore
     * @return Call
     */
    public function setMaxScore(?int $maxScore): Call
    {
        $this->maxScore = $maxScore;
        return $this;
    }

    /**
     *
     * @param int|null $minScore
     * @return Call
     */
    public function setMinScore(?int $minScore): Call
    {
        $this->minScore = $minScore;
        return $this;
    }

    /**
     *
     * @param int $baseScore
     * @return Call
     */
    public function setBaseScore(int $baseScore): Call
    {
        $this->baseScore = $baseScore;
        return $this;
    }

    /**
     *
     * @param Carbon|null $reviewedTime
     * @return Call
     */
    public function setReviewedTime(null|string|Carbon $reviewedTime): Call
    {
        $this->reviewedTime = is_string($reviewedTime) ? Carbon::parse($reviewedTime) : $reviewedTime;
        return $this;
    }

    /**
     * @param int|null $reviewerUserId
     * @return Call
     */
    public function setReviewerUserId(?int $reviewerUserId): Call
    {
        $this->reviewerUserId = $reviewerUserId;
        return $this;
    }

    /**
     *
     * @param int $id
     * @param string $name
     * @return Call
     */
    public function addReviewer(int $id, string $name): Call
    {
        $this->reviewers[] = [
            'id' => $id,
            'name' => $name,
        ];
        $this->reviewers = array_unique($this->reviewers, SORT_REGULAR);
        return $this;
    }

    /**
     *
     * @param bool|null $isTranscribed
     * @return Call|bool
     */
    public function isTranscribed(?bool $isTranscribed = null): Call|bool
    {
        if (is_null($isTranscribed)) {
            return $this->isTranscribed;
        }
        $this->isTranscribed = $isTranscribed;
        return $this;
    }

    /**
     *
     * @param bool|null $isTranslated
     * @return Call|bool
     */
    public function isTranslated(?bool $isTranslated = null): Call|bool
    {
        if (is_null($isTranslated)) {
            return $this->isTranslated;
        }
        $this->isTranslated = $isTranslated;
        return $this;
    }

    /**
     *
     * @param bool|null $isSpeakersRolesDetected
     * @return Call|bool
     */
    public function isSpeakersRolesDetected(?bool $isSpeakersRolesDetected = null): Call|bool
    {
        if (is_null($isSpeakersRolesDetected)) {
            return $this->isSpeakersRolesDetected;
        }

        $this->isSpeakersRolesDetected = $isSpeakersRolesDetected;

        return $this;
    }

    /**
     *
     * @param bool|null $isChecklistCompleted
     * @return Call|bool
     */
    public function isChecklistCompleted(?bool $isChecklistCompleted = null): Call|bool
    {
        if (is_null($isChecklistCompleted)) {
            return $this->isChecklistCompleted;
        }

        $this->isChecklistCompleted = $isChecklistCompleted;

        return $this;
    }

    /**
     * @param bool|null $isSummarizationCompleted
     * @return Call|bool
     */
    public function isSummarizationCompleted(?bool $isSummarizationCompleted = null): Call|bool
    {
        if (is_null($isSummarizationCompleted)) {
            return $this->isSummarizationCompleted;
        }

        $this->isSummarizationCompleted = $isSummarizationCompleted;

        return $this;
    }

    /**
     * @param bool|null $isLlmEventsDetected
     * @return Call|bool|$this
     */
    public function isLlmEventsDetected(?bool $isLlmEventsDetected = null): Call|bool
    {
        if (is_null($isLlmEventsDetected)) {
            return $this->isLlmEventsDetected;
        }
        $this->isLlmEventsDetected = $isLlmEventsDetected;

        return $this;
    }

    /**
     *
     * @param bool|null $isAnalyzed
     * @return Call|bool
     */
    public function isAnalyzed(?bool $isAnalyzed = null): Call|bool
    {
        if (is_null($isAnalyzed)) {
            return $this->isAnalyzed;
        }
        $this->isAnalyzed = $isAnalyzed;
        return $this;
    }

    /**
     *
     * @param bool|null $isReviewed
     * @return bool
     */
    public function isReviewed(?bool $isReviewed = null): Call|bool
    {
        if (is_null($isReviewed)) {
            return $this->isReviewed;
        }
        $this->isReviewed = $isReviewed;
        return $this;
    }

    /**
     *
     * @param bool|null $isSentToTranscribing
     * @return bool|Call|$this
     */
    public function isSentToTranscribing(bool $isSentToTranscribing = null): bool|Call
    {
        if (is_null($isSentToTranscribing)) {
            return $this->isSentToTranscribing;
        }
        $this->isSentToTranscribing = $isSentToTranscribing;
        return $this;
    }


    /**
     *
     * @return int
     */
    public function getScore(): ?int
    {
        if (is_null($this->score)) {
            $this->precalculateValues();
        }
        return $this->score;
    }

    /**
     *
     * @return int|null
     */
    public function getMaxScore(): ?int
    {
        return $this->maxScore;
    }

    /**
     *
     * @return int|null
     */
    public function getMinScore(): ?int
    {
        return $this->minScore;
    }

    /**
     *
     * @return int
     */
    public function getBaseScore(): int
    {
        return $this->baseScore;
    }

    /**
     *
     * @return float
     */
    public function getReviewedScore(): ?float
    {
        return $this->isReviewed() ? $this->getScore() : 0;
    }


    /**
     *
     * @return int
     */
    public function getRiskRank(): ?float
    {
        if (is_null($this->riskRank)) {
            $this->precalculateValues();
        }
        return $this->riskRank;
    }

    /**
     *
     * @return Carbon|null
     */
    public function getReviewedTime(): ?Carbon
    {
        return $this->reviewedTime;
    }

    /**
     *
     * @return int|null
     */
    public function getReviewerUserId(): ?int
    {
        return $this->reviewerUserId;
    }

    /**
     *
     * @return CalculatedEvent\CalculatedEventsCollection|null
     */
    public function getCalculatedEvents(): ?CalculatedEvent\CalculatedEventsCollection
    {
        if (is_null($this->calculatedEvents) && $this->getParagraphs() instanceof ParagraphCollection) {
            $this->calculatedEvents = new CalculatedEvent\CalculatedEventsCollection();

            /** @var Paragraph $paragraph */
            foreach ($this->getParagraphs() as $paragraph) {
                if ($paragraph->getEventHappenings() !== null) {
                    foreach ($paragraph->getEventHappenings() as $eventHappening) {
                        if ($eventHappening->getEvent()->isPinned()) {
                            $this->calculatedEvents->add($eventHappening->getEvent());
                        }
                    }
                }
            }
        }
        return $this->calculatedEvents;
    }

    /**
     *
     * @param bool $force
     * @return Call
     */
    public function precalculateValues(bool $force = false): Call
    {
        if (!is_null($this->score) && !is_null($this->riskRank) && !$force) {
            return $this;
        }
        if (!$this->isAnalyzed()) {
            return $this;
        }
        if (!($this->getParagraphs() instanceof ParagraphCollection)) {
            return $this;
        }

        $eventValues = [];
        $paragraphWithConfirmNeededEvents = [];
        foreach ($this->getParagraphs() as $paragraph) {
            foreach ($paragraph->getEventHappenings() as $eventHappening) {
                if ($eventHappening->isConfirmed() && !$eventHappening->isDeleted()) {
                    $eventValues[$eventHappening->getEvent()->getId()] = $eventHappening->getEvent()->getScore();
                }
                if (!$eventHappening->isConfirmed() && $eventHappening->getEvent()->isConfirmNeeded()) {
                    $paragraphWithConfirmNeededEvents[] = $paragraph->getParagraphNumber();
                }
            }
        }
        $this->score = $this->getBaseScore() + array_sum($eventValues);
        if (!is_null($this->getMinScore())) {
            $this->score = max($this->score, $this->getMinScore());
        }
        if (!is_null($this->getMaxScore())) {
            $this->score = min($this->score, $this->getMaxScore());
        }
        $this->riskRank = $this->getParagraphs()->isEmpty() ? 0 : count(array_unique($paragraphWithConfirmNeededEvents)) / $this->getParagraphs()->count() * 100;
        return $this;
    }

    /**
     *
     * @return EventHappeningCollection
     */
    public function getReviewedEventHappenings(): EventHappeningCollection
    {
        $this->reviewedEventHappenings = new EventHappeningCollection();
        if (!($this->getParagraphs() instanceof ParagraphCollection)) {
            return $this->reviewedEventHappenings;
        }

        /** @var Paragraph $paragraph */
        foreach ($this->getParagraphs() as $paragraph) {
            if ($paragraph->getEventHappenings() !== null) {
                foreach ($paragraph->getEventHappenings() as $eventHappening) {
                    if ($eventHappening->isConfirmed() && !$eventHappening->isDeleted()) {
                        $this->reviewedEventHappenings->add($eventHappening, $paragraph->getParagraphNumber() . '-' . $eventHappening->getEvent()->getId());
                    }
                }
            }
        }
        return $this->reviewedEventHappenings;
    }

    /**
     *
     * @return array
     */
    public function getReviewedEventIds(): array
    {
        $eventIds = [];
        foreach ($this->getReviewedEventHappenings() as $eventHappening) {
            if ($eventHappening->isConfirmed() && !$eventHappening->isDeleted()) {
                $eventIds[] = $eventHappening->getEvent()->getId();
            }
        }
        return array_unique($eventIds);
    }

    /**
     *
     * @return array
     */
    public function getReviewedEventCategoryIds(): array
    {
        $eventCategoryIds = [];
        foreach ($this->getReviewedEventHappenings() as $eventHappening) {
            if ($eventHappening->isConfirmed() && !$eventHappening->isDeleted()) {
                $eventCategoryIds[] = $eventHappening->getEvent()->getCategoryId();
            }
        }
        return array_unique($eventCategoryIds);
    }

    /**
     * @return bool
     */
    public function isPartlyReviewed(): bool
    {
        if (!($this->getParagraphs() instanceof ParagraphCollection)) {
            return false;
        }

        if ($this->isReviewed()) {
            return false;
        }

        /** @var Paragraph $paragraph */
        foreach ($this->getParagraphs() as $paragraph) {
            if ($paragraph->getEventHappenings() !== null) {
                /** @var EventHappening $eventHappening */
                foreach ($paragraph->getEventHappenings() as $eventHappening) {
                    if ($eventHappening->isChanged()) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     *
     * @return int
     */
    public function getQuestionsCount(): int
    {
        return 0;
    }

    /**
     *
     * @return array
     */
    public function getTopics(): array
    {
        return [];
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = $this->extract($this);
        if ($this->getParagraphs() instanceof ParagraphCollection) {
            $result['paragraphs'] = $this->getParagraphs()->toArray();
        } else {
            unset($result['paragraphs']);
        }
        if ($this->getParagraphs() instanceof ParagraphCollection) {
            $result['calculated_events'] = $this->getCalculatedEvents()->toArray();
        } else {
            unset($result['calculated_events']);
        }
        if ($this->getComments() instanceof CommentCollection) {
            $result['comments'] = $this->getComments()->toArray();
        } else {
            unset($result['comments']);
        }
        if ($this->getFragments() instanceof CallFragment\CallFragmentCollection) {
            $result['fragments'] = $this->getFragments()->toArray([
                'color',
                'active_paragraph',
                'start_time',
                'end_time',
            ]);
        } else {
            unset($result['fragments']);
        }
        $result['reviewed_event_happenings'] = $this->getReviewedEventHappenings()->toArray();
        return $result;
    }
}
