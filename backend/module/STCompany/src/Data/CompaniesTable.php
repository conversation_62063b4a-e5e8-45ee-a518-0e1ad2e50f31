<?php

declare(strict_types=1);

namespace STCompany\Data;

use Laminas\Db\ResultSet\ResultSetInterface;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\Company;
use STLib\Db\AbstractTable;

class CompaniesTable extends AbstractTable
{
    /**
     *
     * @return ResultSetInterface
     */
    public function getCompanyIds(): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->columns([
                'company_id'
            ])
            ->where([
                'deleted' => false,
            ]);
        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @return ResultSetInterface
     */
    public function getCompanyIdsWithS3Integration(): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select->columns([
            'company_id'
        ]);
        $select->where([
            'companies.deleted' => false,
            'companies.is_s3_integration_enabled' => true,
        ]);

        return $this->tableGateway->selectWith($select);
    }

    public function getCompanyIdsWithClientSummaryEnabled(): array
    {
        $select = $this->tableGateway->getSql()->select();
        $select->columns([
            'company_id'
        ]);
        $select->where([
            'companies.deleted' => false,
            'companies.is_client_summary_enabled' => true,
        ]);

        return array_map(function ($companyId) {
            return (int) $companyId;
        }, array_column($this->tableGateway->selectWith($select)->toArray(), 'company_id'));
    }

    /**
     *
     * @return \Laminas\Db\ResultSet\ResultSetInterface
     */
    public function getCompanyIdsWithCallsRemovingEnabled(): \Laminas\Db\ResultSet\ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select->columns([
            'company_id'
        ]);
        $select->where([
            'companies.deleted' => false,
        ])
        ->where
        ->isNotNull('companies.days_to_remove_calls');

        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @return ResultSetInterface
     */
    public function getCompanyIdsWithS3ReportIntegration(): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select->columns([
            'company_id'
        ]);
        $select
            ->where([
                'deleted' => false,
            ])
            ->where
            ->isNotNull('aws_s3_report_bucket_region')
            ->and
            ->isNotNull('aws_s3_report_bucket_name');

        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @param int $companyId
     * @return ResultSetInterface
     * @throws NotFoundApiException
     */
    public function getCompany(int $companyId, ?int $userId = null): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select->where([
            'companies.company_id' => $companyId,
            'companies.deleted' => false,
        ]);
        if (!is_null($userId)) {
            $select
                    ->join(
                        [
                            'ucr' => 'users_companies_roles',
                        ],
                        'ucr.company_id = companies.company_id',
                        [],
                        \Laminas\Db\Sql\Select::JOIN_INNER
                    )
                    ->where([
                        'ucr.user_id' => $userId,
                    ]);
        }
        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new NotFoundApiException('Company not found');
        }
        return $result;
    }

    /**
     *
     * @param string $companyToken
     * @return ResultSetInterface
     * @throws NotFoundApiException
     */
    public function getCompanyByApiToken(string $companyToken): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select
                ->join(
                    [
                        'aa' => 'api_applications',
                    ],
                    'aa.company_id = companies.company_id',
                    [],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                )
                ->where([
                    'aa.application_token' => $companyToken,
                    'companies.deleted' => false,
                ]);
        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new NotFoundApiException('Company not found');
        }
        return $result;
    }

    /**
     *
     * @param int $userId
     * @return ResultSetInterface
     */
    public function getCompaniesByUserId(int $userId): ResultSetInterface
    {
        $select = $this->tableGateway->getSql()->select();
        $select
                ->columns([
                    'company_id',
                    'company_name',
                    'aws_s3_bucket_region',
                    'aws_s3_bucket_name',
                    'aws_s3_bucket_dir',
                    'deleted',
                    'threshold_bar',
                    'paid_transcribing_time',
                    'min_score',
                    'max_score',
                    'base_score',
                    'min_call_duration_for_auto_analyze',
                    'is_checklists_enabled',
                    'is_summarization_enabled',
                    'is_export_enabled',
                    'front_id',
                ])
                ->join(
                    [
                        'ucr' => 'users_companies_roles',
                    ],
                    'ucr.company_id = companies.company_id',
                    [],
                    \Laminas\Db\Sql\Select::JOIN_INNER
                )
                ->where([
                    'ucr.user_id' => $userId,
                    'companies.deleted' => false,
                ]);
        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @param Company $company
     * @return int
     */
    public function saveCompany(Company $company): int
    {
        $data = [
            'company_name' => $company->getName(),
            'threshold_bar' => $company->getThresholdBar(),
            'company_avatar' => $company->getAvatar(),
            'aws_s3_bucket_region' => $company->getAwsS3BucketRegion(),
            'aws_s3_bucket_name' => $company->getAwsS3BucketName(),
            'aws_s3_bucket_dir' => $company->getAwsS3BucketDir(),
            'paid_transcribing_time' => $company->getPaidTranscribingTime(),
            'min_call_duration_for_auto_analyze' => $company->getMinCallDurationForAutoAnalyze(),
            'front_id' => $company->getFrontId(),
            'last_export_date' => $company->getLastExportDate(),
        ];

        if (!is_null($company->isDeleted())) {
            $data['deleted'] = $company->isDeleted();
        }

        if ($company->getId() > 0) {
            $this->tableGateway->update($data, [
                'company_id' => $company->getId(),
            ]);
        } else {
            $this->tableGateway->insert($data);
            $company->setId((int) $this->tableGateway->lastInsertValue);
        }

        return $company->getId();
    }

    /**
     *
     * @param int $companyId
     * @return int
     */
    public function deleteCompany(int $companyId): int
    {
        return $this->tableGateway->update([
            'deleted' => true,
        ], [
            'company_id' => $companyId,
        ]);
    }

    public function getCompaniesIdsToExport(): array
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->columns([
                'company_id',
            ])
            ->where([
                'deleted' => 0,
                'is_export_enabled' => 1,
            ]);

        return array_column($this->tableGateway->selectWith($select)->toArray(), 'company_id');
    }

    public function partialUpdateCompany(int $companyId, array $data): void
    {
        $this->tableGateway->update($data, [
            'company_id' => $companyId,
        ]);
    }
}
