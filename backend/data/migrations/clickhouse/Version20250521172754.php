<?php

namespace Clickhouse\Migrations;

class Version20250521172754 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     *
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            CREATE TABLE client_summaries (
                company_id UInt32,
                client_id String,
                primary_purpose String,
                deal_size String,
                timeline Array(Map(String, String)),
                deal_status String,
                next_step String,
                customer_sentiment String,
                client_discovery_parameters Array(String),
                customer_problems Array(Map(String, String)),
                business_opportunities Array(String),
                risks String,
                agent_performance String,
                interaction_notes Array(String),
                last_call_time DateTime,
                last_call_created DateTime,
                created DateTime DEFAULT now()
            )
            ENGINE = MergeTree()
            PARTITION BY toYYYYMM(created)
            ORDER BY (
                company_id,
                client_id,
                last_call_time
            )
            SETTINGS index_granularity = 8192
        ');
    }

    /**
     *
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE
                client_summaries
        ');
    }
}
