<?php

namespace STLib\Validator\Check;

use Lam<PERSON>\Validator\AbstractValidator;

class AiInput extends AbstractValidator
{
    const TOO_SHORT = 'tooShort';
    const DISALLOWED_CHARACTERS = 'disallowedCharacters';
    const CONSECUTIVE_WHITESPACE = 'consecutiveWhitespace';
    const MEANINGLESS_CONTENT = 'meaninglessContent';

    protected $messageTemplates = [
        self::TOO_SHORT => 'Input must be at least 12 characters long.',
        self::DISALLOWED_CHARACTERS => 'Some characters are not allowed. Please use only letters, numbers, basic punctuation (_ - . , ! ? ; : — \' "), spaces, and line breaks.',
        self::CONSECUTIVE_WHITESPACE => 'Please avoid using multiple spaces or empty lines in a row.',
        self::MEANINGLESS_CONTENT => 'Input must contain meaningful content, not just punctuation or spaces.',
    ];

    /**
     * Allowed characters pattern
     * Letters (A-Z, a-z), Numbers (0-9), Spaces, Single line breaks (\n)
     * Punctuation: _ - . , ! ? ; : " ' and Em dash (—)
     */
    private const ALLOWED_CHARACTERS_PATTERN = '/^[A-Za-z0-9\s\n_\-.,!?;:"\'—]*$/u';

    private const CONSECUTIVE_WHITESPACE_PATTERN = '/\s\s+|\n\n+/';

    /**
     * Pattern to check if content has meaningful text (at least 6 letters)
     */
    private const MEANINGFUL_CONTENT_PATTERN = '/[A-Za-z]/';

    private function cleanText(string $value): string
    {
        // Trim leading/trailing whitespace and replace multiple spaces with single space
        $cleaned = preg_replace('/\s\s+/', ' ', trim($value));
        
        // Replace multiple line breaks with single line break
        return preg_replace('/\n\n+/', "\n", $cleaned);
    }

    private function countLetters(string $value): int
    {
        return preg_match_all(self::MEANINGFUL_CONTENT_PATTERN, $value);
    }

    /**
     * Check if content is meaningful (not just punctuation or spaces)
     */
    private function hasMeaningfulContent(string $value): bool
    {
        // Remove all allowed punctuation and whitespace
        $contentOnly = preg_replace('/[\s\n_\-.,!?;:"\'—]/u', '', $value);
        
        // Check if there's any alphanumeric content left
        return !empty($contentOnly) && preg_match('/[A-Za-z0-9]/', $contentOnly);
    }

    public function isValid($value): bool
    {
        if (!is_string($value)) {
            $value = (string) $value;
        }

        $this->setValue($value);

        // Check for disallowed characters first (before cleaning)
        if (!preg_match(self::ALLOWED_CHARACTERS_PATTERN, $value)) {
            $this->error(self::DISALLOWED_CHARACTERS);
            return false;
        }

        // Check for consecutive whitespace (before cleaning)
        if (preg_match(self::CONSECUTIVE_WHITESPACE_PATTERN, $value)) {
            $this->error(self::CONSECUTIVE_WHITESPACE);
            return false;
        }

        $cleanedValue = $this->cleanText($value);

        if (mb_strlen($cleanedValue) < 12) {
            $this->error(self::TOO_SHORT);
            return false;
        }

        if (!$this->hasMeaningfulContent($cleanedValue)) {
            $this->error(self::MEANINGLESS_CONTENT);
            return false;
        }

        if ($this->countLetters($cleanedValue) < 6) {
            $this->error(self::MEANINGLESS_CONTENT);
            return false;
        }

        return true;
    }
}
