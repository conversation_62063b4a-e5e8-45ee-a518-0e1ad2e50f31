<?php

declare(strict_types=1);

namespace tests\Unit\base\STLib\Validator\Check;

use PHPUnit\Framework\TestCase;
use STLib\Validator\Check\AiInput;

final class AiInputTest extends TestCase
{
    private AiInput $validator;

    protected function setUp(): void
    {
        $this->validator = new AiInput();
    }

    public function testValidInputPasses(): void
    {
        $validInputs = [
            'This is a valid input with enough characters and letters.',
            'Hello world! This text contains 4 chars and 6 letters.',
            'Testing with numbers 123 and punctuation: - _ . , ! ? ; : " \' —',
            'Multi-line text
with line breaks
should work fine.',
            'Text with em dash — and various punctuation marks.',
            'Text with new symbols: / \\ ( ) should work.',
            'Path/to/file.txt (example) works now.',
        ];

        foreach ($validInputs as $input) {
            $this->assertTrue(
                $this->validator->isValid($input),
                "Input should be valid: '$input'. Errors: " . implode(', ', $this->validator->getMessages())
            );
        }
    }

    public function testTooShortInputFails(): void
    {
        $shortInputs = [
            'a',
            'ab',
            'abc', // exactly 3 characters
        ];

        foreach ($shortInputs as $input) {
            $this->assertFalse(
                $this->validator->isValid($input),
                "Input should be invalid (too short): '$input'"
            );
            $this->assertArrayHasKey(AiInput::TOO_SHORT, $this->validator->getMessages());
        }
    }

    public function testMinimumLengthPasses(): void
    {
        // Test that 4 characters is now the minimum (but still needs 6 letters)
        $validInputs = [
            'abcdef', // exactly 6 characters and 6 letters
            'abcdefg', // 7 characters and 7 letters
        ];

        foreach ($validInputs as $input) {
            $this->assertTrue(
                $this->validator->isValid($input),
                "Input should be valid (4+ chars, 6+ letters): '$input'. Errors: " . implode(', ', $this->validator->getMessages())
            );
        }
    }

    public function testDisallowedCharactersFail(): void
    {
        $invalidInputs = [
            'Text with emoji 😀 should fail',
            'Text with @ should fail',
            'Text with # should fail',
            'Text with $ sign should fail',
            'Text with % should fail',
            'Text with & should fail',
            'Text with * should fail',
            'Text with + should fail',
            'Text with = should fail',
            'Text with [] should fail',
            'Text with {} should fail',
            'Text with | should fail',
            'Text with < should fail',
            'Text with > should fail',
            'Text with ^ should fail',
            'Text with ~ should fail',
            'Text with ` should fail',
        ];

        foreach ($invalidInputs as $input) {
            $this->assertFalse(
                $this->validator->isValid($input),
                "Input should be invalid (disallowed characters): '$input'"
            );
            $this->assertArrayHasKey(AiInput::DISALLOWED_CHARACTERS, $this->validator->getMessages());
        }
    }

    public function testConsecutiveWhitespaceFails(): void
    {
        $invalidInputs = [
            'Text with    multiple spaces should fail',
            "Text with\n\ndouble line breaks should fail",
            'Text with multiple  spaces  in  different  places should fail',
        ];

        foreach ($invalidInputs as $input) {
            $this->assertFalse(
                $this->validator->isValid($input),
                "Input should be invalid (consecutive whitespace): '$input'"
            );
            $this->assertArrayHasKey(AiInput::CONSECUTIVE_WHITESPACE, $this->validator->getMessages());
        }
    }

    public function testMeaninglessContentFails(): void
    {
        // Test input with only punctuation (long enough, no consecutive spaces)
        $onlyPunctuation = '.,!?;:"\'— .,!?;:"\'— .,!?;:"\'—';
        $this->assertFalse($this->validator->isValid($onlyPunctuation));
        $messages = $this->validator->getMessages();
        $this->assertArrayHasKey(AiInput::MEANINGLESS_CONTENT, $messages,
            "Only punctuation should fail with MEANINGLESS_CONTENT. Got: " . implode(', ', array_keys($messages)));

        // Test input with less than 6 letters (long enough, no consecutive spaces)
        $fewLetters = 'abcde 1234567890';
        $this->assertFalse($this->validator->isValid($fewLetters));
        $messages = $this->validator->getMessages();
        $this->assertArrayHasKey(AiInput::MEANINGLESS_CONTENT, $messages,
            "Less than 6 letters should fail with MEANINGLESS_CONTENT. Got: " . implode(', ', array_keys($messages)));
    }

    public function testMinimumLetterRequirement(): void
    {
        // Test inputs with exactly 6 letters (should pass)
        $validInputs = [
            'a b c d e f .,!?;:"\'— .,!?;:"\'—', // exactly 6 letters
            '123456 abcdef .,!?;:"\'—', // 6 letters with numbers and punctuation
            'abcdef (test)', // 10 letters with new allowed symbols
        ];

        foreach ($validInputs as $input) {
            $this->assertTrue(
                $this->validator->isValid($input),
                "Input should be valid (6 or more letters): '$input'. Errors: " . implode(', ', $this->validator->getMessages())
            );
        }

        // Test inputs with less than 6 letters (should fail)
        $invalidInputs = [
            'abcde 12345', // exactly 5 letters, long enough
            'abcd 123456', // exactly 4 letters, long enough
        ];

        foreach ($invalidInputs as $input) {
            $this->assertFalse(
                $this->validator->isValid($input),
                "Input should be invalid (less than 6 letters): '$input'"
            );
            $this->assertArrayHasKey(AiInput::MEANINGLESS_CONTENT, $this->validator->getMessages());
        }
    }

    public function testEmptyAndWhitespaceOnlyInputs(): void
    {
        // Test empty string
        $this->assertFalse($this->validator->isValid(''));
        $messages = $this->validator->getMessages();
        $this->assertArrayHasKey(AiInput::TOO_SHORT, $messages);

        // Test single spaces (should fail on CONSECUTIVE_WHITESPACE)
        $this->assertFalse($this->validator->isValid('   '));
        $messages = $this->validator->getMessages();
        $this->assertArrayHasKey(AiInput::CONSECUTIVE_WHITESPACE, $messages);

        // Test single line break (should fail on TOO_SHORT)
        $this->assertFalse($this->validator->isValid("\n"));
        $messages = $this->validator->getMessages();
        $this->assertArrayHasKey(AiInput::TOO_SHORT, $messages);

        // Test double line breaks (should fail on CONSECUTIVE_WHITESPACE)
        $this->assertFalse($this->validator->isValid("\n\n"));
        $messages = $this->validator->getMessages();
        $this->assertArrayHasKey(AiInput::CONSECUTIVE_WHITESPACE, $messages);

        // Test mixed whitespace with consecutive spaces (should fail on CONSECUTIVE_WHITESPACE)
        $this->assertFalse($this->validator->isValid("   \n   \n   "));
        $messages = $this->validator->getMessages();
        $this->assertTrue(
            isset($messages[AiInput::CONSECUTIVE_WHITESPACE]) || isset($messages[AiInput::TOO_SHORT]),
            "Should have CONSECUTIVE_WHITESPACE or TOO_SHORT error. Got: " . implode(', ', array_keys($messages))
        );
    }

    public function testAllowedCharactersPattern(): void
    {
        // Test all explicitly allowed characters
        $allowedChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789 _-.,!?;:"\'—/\\()';
        $testInput = $allowedChars . ' This contains all allowed characters.';

        $this->assertTrue(
            $this->validator->isValid($testInput),
            "Input with all allowed characters should be valid. Errors: " . implode(', ', $this->validator->getMessages())
        );
    }

    public function testNewAllowedSymbols(): void
    {
        // Test the newly allowed symbols specifically
        $validInputs = [
            'File path/to/file.txt works',
            'Windows path\\to\\file.txt works',
            'Function call(parameter) works',
            'Mixed symbols: /\\() all work',
        ];

        foreach ($validInputs as $input) {
            $this->assertTrue(
                $this->validator->isValid($input),
                "Input with new allowed symbols should be valid: '$input'. Errors: " . implode(', ', $this->validator->getMessages())
            );
        }
    }
}
