<?php

declare(strict_types=1);

namespace tests\Feature\LlmEvents;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Data\CompanyLlmEventsTable;
use STCompany\Data\PermissionsTable;
use STCompany\Entity\LlmEvent\LlmEvent as CompanyLlmEvent;
use STCompany\Entity\Role;
use STLlmEvent\Data\LlmEventsTable;
use STLlmEvent\Entity\LlmEvent;
use tests\Feature\AuthTestCase;

final class UpdateLlmEventTest extends AuthTestCase
{
    /**
     * @throws Exception
     * @throws PHPUnitException
     */
    public function testUpdate(): void
    {
        $this->company->method('isManageLlmEventsByUsersEnabled')->willReturn(true);

        $llmEventId = $this->faker->numberBetween(1, 100);

        $name = $this->faker->word();
        $description = $this->faker->text();

        $existentLlmEvent = $this->createMock(LlmEvent::class);
        $existentCompanyLlmEvent = $this->createMock(CompanyLlmEvent::class);

        $llmEventsTable = $this->createMock(LlmEventsTable::class);
        $llmEventsTable->method('getLlmEvent')->with($llmEventId)->willReturn($existentLlmEvent);
        $llmEventsTable
            ->expects($this->once())
            ->method('saveLlmEvent')
            ->with(
                self::callback(
                    function (LlmEvent $llmEvent) use ($llmEventId, $name, $description): bool {
                        return $llmEvent->getId() === $llmEventId
                            && $name === $llmEvent->getName()
                            && $description === $llmEvent->getDescription();
                    }
                )
            );
        $this->serviceManager->setService(LlmEventsTable::class, $llmEventsTable);

        $companyLlmEventsTable = $this->createMock(CompanyLlmEventsTable::class);
        $companyLlmEventsTable
            ->method('getLlmEvent')
            ->with($llmEventId, $this->companyId)
            ->willReturn($existentCompanyLlmEvent);
        $companyLlmEventsTable
            ->method('isLlmEventConnectedToAnotherCompany')
            ->with($llmEventId, $this->companyId)
            ->willReturn(false);
        $this->serviceManager->setService(CompanyLlmEventsTable::class, $companyLlmEventsTable);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::EVENTS,
            PermissionsTable::WRITE_PERMISSION
        );

        $this->getRequest()->setContent(json_encode([
            'name' => $name,
            'description' => $description,
        ]));
        $this->dispatchApi('llm-event/' . $llmEventId, 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $expectedData = [
            'event' => [
                'id' => $llmEventId,
                'name' => $name,
                'description' => $description,
            ]
        ];

        $this->assertSame($expectedData, $response['result']);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateWhenEventNotConnectedToCompany(): void
    {
        $this->company->method('isManageLlmEventsByUsersEnabled')->willReturn(true);

        $llmEventId = $this->faker->numberBetween(1, 100);

        $existentLlmEvent = $this->createMock(LlmEvent::class);

        $llmEventsTable = $this->createMock(LlmEventsTable::class);
        $llmEventsTable->method('getLlmEvent')->with($llmEventId)->willReturn($existentLlmEvent);
        $this->serviceManager->setService(LlmEventsTable::class, $llmEventsTable);

        $companyLlmEventsTable = $this->createMock(CompanyLlmEventsTable::class);
        $companyLlmEventsTable
            ->method('getLlmEvent')
            ->with($llmEventId, $this->companyId)
            ->willThrowException(new NotFoundApiException('Company llm event not found'));
        $this->serviceManager->setService(CompanyLlmEventsTable::class, $companyLlmEventsTable);

        $this->loginAs(Role::COMPANY_ADMIN_ROLE_TYPE);
        $this->dispatchApi('llm-event/' . $llmEventId, 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'Company llm event not found',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(404);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateWhenEventConnectedToAnotherCompany(): void
    {
        $this->company->method('isManageLlmEventsByUsersEnabled')->willReturn(true);

        $llmEventId = $this->faker->numberBetween(1, 100);

        $existentLlmEvent = $this->createMock(LlmEvent::class);
        $existentCompanyLlmEvent = $this->createMock(CompanyLlmEvent::class);

        $llmEventsTable = $this->createMock(LlmEventsTable::class);
        $llmEventsTable->method('getLlmEvent')->with($llmEventId)->willReturn($existentLlmEvent);
        $this->serviceManager->setService(LlmEventsTable::class, $llmEventsTable);

        $companyLlmEventsTable = $this->createMock(CompanyLlmEventsTable::class);
        $companyLlmEventsTable
            ->method('getLlmEvent')
            ->with($llmEventId, $this->companyId)
            ->willReturn($existentCompanyLlmEvent);
        $companyLlmEventsTable
            ->method('isLlmEventConnectedToAnotherCompany')
            ->with($llmEventId, $this->companyId)
            ->willReturn(true);
        $this->serviceManager->setService(CompanyLlmEventsTable::class, $companyLlmEventsTable);

        $this->loginAs(Role::COMPANY_ADMIN_ROLE_TYPE);
        $this->dispatchApi('llm-event/' . $llmEventId, 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'This event is also used by other companies and cannot be modified. To make changes, please create your own copy.',
            $response['error']['message']
        );

        $this->assertResponseStatusCode(422);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateWhenManageDisabled(): void
    {
        $this->company->method('isManageLlmEventsByUsersEnabled')->willReturn(false);

        $llmEventId = $this->faker->numberBetween(1, 100);

        $this->loginAs(Role::COMPANY_ADMIN_ROLE_TYPE);
        $this->dispatchApi('llm-event/' . $llmEventId, 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'LLM event manage is currently disabled for your company. Contact support for assistance.',
            $response['error']['message']
        );

        $this->assertResponseStatusCode(403);
    }

    public function testUpdateWhenReadPermission(): void
    {
        $llmEventId = $this->faker->numberBetween(1, 100);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::EVENTS,
            PermissionsTable::READ_PERMISSION
        );

        $this->dispatchApi('llm-event/' . $llmEventId, 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User has required permission, but doesn\'t have adequate access level',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateWhenNoPermissions(): void
    {
        $llmEventId = $this->faker->numberBetween(1, 100);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('llm-event/' . $llmEventId, 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User doesn\'t have required permission',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }
}
