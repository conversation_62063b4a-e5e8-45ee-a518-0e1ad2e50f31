<?php

declare(strict_types=1);

namespace tests\Feature\Admin\LlmEvents;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STCompany\Entity\Role;
use STLlmEvent\Data\LlmEventsTable;
use STLlmEvent\Entity\LlmEvent;
use tests\Feature\AuthTestCase;

final class CreateLlmEventTest extends AuthTestCase
{
    /**
     * @throws Exception
     * @throws PHPUnitException
     */
    public function testTrimNameOnCreate(): void
    {
        $eventName = $this->faker->word();
        $description = $this->faker->text();

        $llmEventsTable = $this->createMock(LlmEventsTable::class);
        $llmEventsTable
            ->expects($this->once())
            ->method('saveLlmEvent')
            ->with(
                self::callback(
                    function (LlmEvent $llmEvent) use ($eventName, $description) {
                        return $llmEvent->getName() === $eventName &&
                            $llmEvent->getDescription() === $description;
                    }
                ),
            );
        $this->serviceManager->setService(LlmEventsTable::class, $llmEventsTable);

        $this->getRequest()->setContent(json_encode([
            'name' => ' ' . $eventName . " \n",
            'description' => $description
        ]));

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->dispatchApi('admin/llm-events', 'POST');
    }
}
